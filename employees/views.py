from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth import get_user_model

User = get_user_model()


@login_required
def employee_list(request):
    """قائمة الموظفين"""
    employees = User.objects.filter(is_active_employee=True).order_by('-date_joined')

    # Table configuration for unified responsive table
    employees_table_config = {
        'title': 'قائمة الموظفين',
        'icon': 'fas fa-users',
        'columns': [
            {
                'key': 'get_full_name',
                'label': 'الاسم',
                'type': 'avatar',
                'avatar_field': 'get_full_name',
                'subtitle_field': 'username',
            },
            {
                'key': 'email',
                'label': 'البريد الإلكتروني',
                'type': 'text',
            },
            {
                'key': 'get_role_display',
                'label': 'الدور',
                'type': 'text',
            },
            {
                'key': 'date_joined',
                'label': 'تاريخ الانضمام',
                'type': 'date',
                'date_format': 'Y/m/d',
            },
            {
                'key': 'is_active',
                'label': 'الحالة',
                'type': 'status',
                'status_map': {
                    True: 'success',
                    False: 'danger',
                },
                'display_method': 'get_status_display',
            },
        ],
        'actions': [
            {
                'label': 'عرض',
                'icon': 'fas fa-eye',
                'url_name': 'employees:detail',
                'class': 'unified-btn-primary',
                'show_label': True,
            },
            {
                'label': 'تعديل',
                'icon': 'fas fa-edit',
                'url_name': 'employees:edit',
                'class': 'unified-btn-secondary',
                'show_label': False,
            },
        ]
    }

    context = {
        'employees': employees,
        'employees_table_config': employees_table_config,
    }

    return render(request, 'employees/list.html', context)


@login_required
def employee_detail(request, pk):
    """تفاصيل الموظف"""
    employee = get_object_or_404(User, pk=pk)
    return render(request, 'employees/detail.html', {'employee': employee})


@login_required
def employee_create(request):
    """إنشاء موظف جديد"""
    messages.info(request, 'صفحة إنشاء الموظفين قيد التطوير')
    return redirect('employees:list')


@login_required
def employee_edit(request, pk):
    """تعديل الموظف"""
    messages.info(request, 'صفحة تعديل الموظفين قيد التطوير')
    return redirect('employees:detail', pk=pk)


@login_required
def employee_delete(request, pk):
    """حذف الموظف"""
    messages.info(request, 'وظيفة حذف الموظفين قيد التطوير')
    return redirect('employees:list')
