"""
Template tags and filters for unified responsive tables
"""

from django import template
from django.utils.safestring import mark_safe
import datetime

register = template.Library()


@register.filter
def getattr(obj, attr_name):
    """
    Get attribute value from object
    Usage: {{ object|getattr:"field_name" }}
    """
    try:
        if hasattr(obj, attr_name):
            value = getattr(obj, attr_name)
            # If it's a method, call it
            if callable(value):
                return value()
            return value
        return ""
    except (AttributeError, TypeError):
        return ""


@register.filter
def get_item(dictionary, key):
    """
    Get item from dictionary
    Usage: {{ dict|get_item:"key" }}
    """
    try:
        return dictionary.get(key, "")
    except (AttributeError, TypeError):
        return ""


@register.filter
def format_currency(value, currency="ر.س"):
    """
    Format currency value
    Usage: {{ amount|format_currency:"USD" }}
    """
    try:
        if value is None:
            return f"0.00 {currency}"
        return f"{float(value):,.2f} {currency}"
    except (ValueError, TypeError):
        return f"0.00 {currency}"


@register.filter
def format_progress(value):
    """
    Format progress percentage
    Usage: {{ progress|format_progress }}
    """
    try:
        progress = int(value) if value is not None else 0
        return min(max(progress, 0), 100)  # Clamp between 0-100
    except (ValueError, TypeError):
        return 0


@register.filter
def status_class(status, status_map=None):
    """
    Get CSS class for status
    Usage: {{ status|status_class:status_map }}
    """
    if status_map and isinstance(status_map, dict):
        return status_map.get(status, 'info')
    
    # Default status mapping
    default_map = {
        'active': 'success',
        'inactive': 'danger',
        'pending': 'warning',
        'completed': 'success',
        'cancelled': 'danger',
        'in_progress': 'info',
        'planning': 'warning',
        'testing': 'info',
        'deployed': 'success',
    }
    
    return default_map.get(status, 'info')


@register.filter
def avatar_initial(name):
    """
    Get initial letter for avatar
    Usage: {{ name|avatar_initial }}
    """
    if not name:
        return "?"
    
    # Handle Arabic names
    name = str(name).strip()
    if name:
        return name[0].upper()
    return "?"


@register.filter
def truncate_smart(value, length=50):
    """
    Smart truncation that respects word boundaries
    Usage: {{ text|truncate_smart:30 }}
    """
    if not value:
        return ""
    
    value = str(value)
    if len(value) <= length:
        return value
    
    # Find last space before the limit
    truncated = value[:length]
    last_space = truncated.rfind(' ')
    
    if last_space > 0:
        return truncated[:last_space] + "..."
    else:
        return truncated + "..."


@register.filter
def format_date_arabic(date_value, format_string="Y/m/d"):
    """
    Format date with Arabic-friendly format
    Usage: {{ date|format_date_arabic:"Y/m/d" }}
    """
    if not date_value:
        return ""
    
    try:
        if isinstance(date_value, str):
            # Try to parse string date
            date_value = datetime.datetime.strptime(date_value, "%Y-%m-%d").date()
        
        # Format the date
        if format_string == "Y/m/d":
            return date_value.strftime("%Y/%m/%d")
        elif format_string == "d/m/Y":
            return date_value.strftime("%d/%m/%Y")
        else:
            return date_value.strftime(format_string)
    except (ValueError, AttributeError):
        return str(date_value)


@register.filter
def phone_format(phone):
    """
    Format phone number for display
    Usage: {{ phone|phone_format }}
    """
    if not phone:
        return ""
    
    phone = str(phone).strip()
    # Remove any non-digit characters except +
    import re
    phone = re.sub(r'[^\d+]', '', phone)
    
    # Format Saudi phone numbers
    if phone.startswith('+966'):
        return f"+966 {phone[4:7]} {phone[7:10]} {phone[10:]}"
    elif phone.startswith('966'):
        return f"+966 {phone[3:6]} {phone[6:9]} {phone[9:]}"
    elif phone.startswith('05'):
        return f"+966 {phone[1:4]} {phone[4:7]} {phone[7:]}"
    
    return phone


@register.filter
def email_domain(email):
    """
    Extract domain from email
    Usage: {{ email|email_domain }}
    """
    if not email or '@' not in email:
        return ""
    
    return email.split('@')[1]


@register.simple_tag
def table_sort_url(request, field):
    """
    Generate URL for table sorting
    Usage: {% table_sort_url request "field_name" %}
    """
    params = request.GET.copy()
    current_sort = params.get('sort', '')
    
    if current_sort == field:
        # Reverse sort
        params['sort'] = f"-{field}"
    elif current_sort == f"-{field}":
        # Remove sort
        if 'sort' in params:
            del params['sort']
    else:
        # Set new sort
        params['sort'] = field
    
    if params:
        return f"?{params.urlencode()}"
    return ""


@register.simple_tag
def table_sort_icon(request, field):
    """
    Get sort icon for table header
    Usage: {% table_sort_icon request "field_name" %}
    """
    current_sort = request.GET.get('sort', '')
    
    if current_sort == field:
        return mark_safe('<i class="fas fa-sort-up"></i>')
    elif current_sort == f"-{field}":
        return mark_safe('<i class="fas fa-sort-down"></i>')
    else:
        return mark_safe('<i class="fas fa-sort"></i>')


@register.inclusion_tag('includes/pagination.html', takes_context=True)
def render_pagination(context, page_obj):
    """
    Render pagination for tables
    Usage: {% render_pagination page_obj %}
    """
    return {
        'page_obj': page_obj,
        'request': context['request'],
    }


@register.filter
def add_class(field, css_class):
    """
    Add CSS class to form field
    Usage: {{ field|add_class:"form-control" }}
    """
    return field.as_widget(attrs={"class": css_class})


@register.filter
def multiply(value, arg):
    """
    Multiply two values
    Usage: {{ value|multiply:2 }}
    """
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0


@register.filter
def percentage(value, total):
    """
    Calculate percentage
    Usage: {{ value|percentage:total }}
    """
    try:
        if float(total) == 0:
            return 0
        return (float(value) / float(total)) * 100
    except (ValueError, TypeError, ZeroDivisionError):
        return 0
