/* Modern Components for Layal ERP */

/* Sidebar Component */
.sidebar {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  min-height: 100vh;
  padding: var(--space-6);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow-y: auto;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: -1;
}

.sidebar-brand {
  text-align: center;
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-brand h1 {
  color: white;
  font-size: var(--font-size-3xl);
  font-weight: 800;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.sidebar-brand .subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-sm);
  margin-top: var(--space-2);
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav-item {
  margin-bottom: var(--space-2);
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
  font-weight: 500;
}

.sidebar-nav-link:hover,
.sidebar-nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateX(-5px);
  box-shadow: var(--shadow-md);
}

.sidebar-nav-link i {
  margin-inline-end: var(--space-3);
  width: 20px;
  text-align: center;
  font-size: var(--font-size-lg);
}

/* Stats Card Component */
.stats-card {
  background: white;
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--color-primary);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  opacity: 0.05;
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-2xl);
}

.stats-card.success {
  border-left-color: var(--color-success);
}

.stats-card.warning {
  border-left-color: var(--color-warning);
}

.stats-card.danger {
  border-left-color: var(--color-danger);
}

.stats-card.info {
  border-left-color: var(--color-info);
}

.stats-number {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  color: var(--color-primary);
  margin-bottom: var(--space-2);
  line-height: 1;
}

.stats-label {
  color: var(--color-gray-600);
  font-weight: 600;
  font-size: var(--font-size-lg);
}

.stats-icon {
  position: absolute;
  top: var(--space-4);
  left: var(--space-4);
  font-size: var(--font-size-3xl);
  color: var(--color-gray-200);
  opacity: 0.5;
}

/* Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  font-family: var(--font-family-arabic);
  font-size: var(--font-size-base);
  line-height: 1;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
}

.btn-secondary {
  background: var(--color-gray-200);
  color: var(--color-gray-700);
}

.btn-secondary:hover {
  background: var(--color-gray-300);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--color-success);
  color: white;
}

.btn-warning {
  background: var(--color-warning);
  color: white;
}

.btn-danger {
  background: var(--color-danger);
  color: white;
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
}

/* Card Component */
.card {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: var(--transition-normal);
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  color: white;
  padding: var(--space-6);
  font-weight: 700;
  font-size: var(--font-size-xl);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-4) var(--space-6);
  background: var(--color-gray-50);
  border-top: 1px solid var(--color-gray-200);
}

/* Form Components */
.form-group {
  margin-bottom: var(--space-5);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 600;
  color: var(--color-gray-700);
}

.form-control {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  font-family: var(--font-family-arabic);
  font-size: var(--font-size-base);
  transition: var(--transition-fast);
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-control:invalid {
  border-color: var(--color-danger);
}

/* Table Component */
.table-container {
  background: white;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.table thead th {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  color: white;
  padding: var(--space-4) var(--space-6);
  font-weight: 700;
  text-align: right;
  border: none;
}

.table tbody td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
  vertical-align: middle;
}

.table tbody tr:hover {
  background: var(--color-gray-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* Alert Component */
.alert {
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  border: none;
  font-weight: 500;
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
  border-left: 4px solid var(--color-success);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--color-warning);
  border-left: 4px solid var(--color-warning);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--color-danger);
  border-left: 4px solid var(--color-danger);
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--color-info);
  border-left: 4px solid var(--color-info);
}

/* Badge Component */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: var(--color-primary);
  color: white;
}

.badge-success {
  background: var(--color-success);
  color: white;
}

.badge-warning {
  background: var(--color-warning);
  color: white;
}

.badge-danger {
  background: var(--color-danger);
  color: white;
}

/* Progress Component */
.progress {
  width: 100%;
  height: 8px;
  background: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

/* Avatar Component */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: var(--font-size-lg);
}

.avatar-lg {
  width: 60px;
  height: 60px;
  font-size: var(--font-size-2xl);
}

.avatar-sm {
  width: 32px;
  height: 32px;
  font-size: var(--font-size-sm);
}

/* ========================================
   UNIFIED RESPONSIVE TABLE INFRASTRUCTURE
   ======================================== */

/* Base responsive table container */
.unified-responsive-table {
  width: 100%;
  font-family: 'IBM Plex Sans Arabic', sans-serif;
  direction: rtl;
}

/* Desktop table styles (≥992px) */
.unified-table-desktop {
  display: block;
  width: 100%;
}

.unified-table-desktop .table {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: none;
  margin-bottom: 0;
}

.unified-table-desktop .table thead th {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border: none;
  font-weight: 600;
  padding: 20px;
  font-size: 0.95rem;
  text-align: right;
  vertical-align: middle;
  white-space: nowrap;
}

.unified-table-desktop .table tbody td {
  padding: 18px 20px;
  border-color: rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.8);
  vertical-align: middle;
  font-size: 0.9rem;
}

.unified-table-desktop .table tbody tr {
  transition: all 0.3s ease;
}

.unified-table-desktop .table tbody tr:hover {
  background: rgba(99, 102, 241, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Tablet card layout (768px-991px) */
.unified-cards-tablet {
  display: none;
  padding: 0;
}

.unified-card-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(99, 102, 241, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.unified-card-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.unified-card-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.unified-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.unified-card-title {
  font-weight: 600;
  font-size: 1.1rem;
  color: #1f2937;
  margin: 0;
  flex-grow: 1;
}

.unified-card-subtitle {
  font-size: 0.85rem;
  color: #6b7280;
  margin: 5px 0 0 0;
}

.unified-card-body {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.unified-card-field {
  display: flex;
  flex-direction: column;
}

.unified-card-field-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.unified-card-field-value {
  font-size: 0.9rem;
  color: #1f2937;
  font-weight: 500;
}

.unified-card-actions {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* Mobile card layout (<768px) */
.unified-cards-mobile {
  display: none;
  padding: 0;
}

.unified-mobile-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #6366f1;
  transition: all 0.3s ease;
}

.unified-mobile-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.unified-mobile-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.unified-mobile-title {
  font-weight: 600;
  font-size: 1rem;
  color: #1f2937;
  margin: 0;
  flex-grow: 1;
}

.unified-mobile-subtitle {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 3px 0 0 0;
}

.unified-mobile-body {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.unified-mobile-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.unified-mobile-field-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6b7280;
  min-width: 80px;
}

.unified-mobile-field-value {
  font-size: 0.85rem;
  color: #1f2937;
  font-weight: 500;
  text-align: left;
  flex-grow: 1;
}

.unified-mobile-actions {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* User avatar component for tables */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
  text-transform: uppercase;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Touch-friendly buttons for mobile */
.unified-btn-mobile {
  min-height: 44px;
  min-width: 44px;
  padding: 10px 16px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.unified-btn-primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
}

.unified-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
  color: white;
}

.unified-btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.unified-btn-secondary:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

/* Status badges */
.unified-status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.unified-status-success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.unified-status-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.unified-status-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.unified-status-info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Progress bars */
.unified-progress {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.unified-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.unified-progress-text {
  font-size: 0.8rem;
  color: #6b7280;
  font-weight: 500;
}

/* ========================================
   RESPONSIVE BREAKPOINTS & MEDIA QUERIES
   ======================================== */

/* Desktop (≥992px) - Show tables, hide cards */
@media (min-width: 992px) {
  .unified-table-desktop {
    display: block !important;
  }

  .unified-cards-tablet {
    display: none !important;
  }

  .unified-cards-mobile {
    display: none !important;
  }

  /* Sidebar adjustments for desktop */
  .sidebar {
    position: relative;
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    padding: 30px;
  }

  .sidebar-overlay {
    display: none !important;
  }
}

/* Tablet (768px-991px) - Show tablet cards, hide table and mobile cards */
@media (min-width: 768px) and (max-width: 991px) {
  .unified-table-desktop {
    display: none !important;
  }

  .unified-cards-tablet {
    display: block !important;
  }

  .unified-cards-mobile {
    display: none !important;
  }

  /* Tablet card grid - 2-3 cards per row */
  .unified-cards-tablet {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 0;
  }

  /* Sidebar becomes collapsible */
  .sidebar {
    position: fixed;
    top: 0;
    right: -100%;
    width: 280px;
    height: 100vh;
    z-index: 1050;
    transition: right 0.3s ease;
  }

  .sidebar.show {
    right: 0;
  }

  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  .main-content {
    margin-left: 0;
    padding: 20px;
  }

  /* Show hamburger menu */
  .navbar-toggler {
    display: block !important;
  }
}

/* Mobile (<768px) - Show mobile cards, hide table and tablet cards */
@media (max-width: 767px) {
  .unified-table-desktop {
    display: none !important;
  }

  .unified-cards-tablet {
    display: none !important;
  }

  .unified-cards-mobile {
    display: block !important;
  }

  /* Mobile single-column layout */
  .unified-cards-mobile {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 0;
  }

  /* Mobile sidebar */
  .sidebar {
    position: fixed;
    top: 0;
    right: -100%;
    width: 280px;
    height: 100vh;
    z-index: 1050;
    transition: right 0.3s ease;
  }

  .sidebar.show {
    right: 0;
  }

  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  .main-content {
    margin-left: 0;
    padding: 15px;
  }

  /* Show hamburger menu */
  .navbar-toggler {
    display: block !important;
  }

  /* Ensure 44px touch targets */
  .unified-btn-mobile,
  .btn,
  .unified-mobile-actions .btn {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-specific adjustments */
  .unified-mobile-field {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .unified-mobile-field-label {
    min-width: auto;
  }

  .unified-mobile-field-value {
    text-align: right;
    width: 100%;
  }

  /* Card header adjustments for mobile */
  .card-header h5 {
    font-size: 1rem;
  }

  .page-title {
    font-size: 1.8rem;
  }
}

/* Extra small devices (<576px) */
@media (max-width: 575px) {
  .unified-mobile-card {
    margin-left: -5px;
    margin-right: -5px;
    border-radius: 8px;
  }

  .main-content {
    padding: 10px;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .card-header h5 {
    font-size: 0.9rem;
  }

  .unified-mobile-actions {
    flex-direction: column;
    gap: 8px;
  }

  .unified-btn-mobile {
    width: 100%;
    justify-content: center;
  }
}

/* ========================================
   HAMBURGER MENU & NAVIGATION
   ======================================== */

.navbar-toggler {
  display: none;
  background: none;
  border: none;
  padding: 8px;
  margin: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.navbar-toggler:hover {
  background: rgba(99, 102, 241, 0.1);
}

.navbar-toggler-icon {
  width: 24px;
  height: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  cursor: pointer;
}

.navbar-toggler-icon span {
  display: block;
  height: 3px;
  width: 100%;
  background: #6366f1;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.navbar-toggler.active .navbar-toggler-icon span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.navbar-toggler.active .navbar-toggler-icon span:nth-child(2) {
  opacity: 0;
}

.navbar-toggler.active .navbar-toggler-icon span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* ========================================
   ACCESSIBILITY & RTL SUPPORT
   ======================================== */

/* Ensure proper RTL support */
[dir="rtl"] .unified-responsive-table {
  direction: rtl;
}

[dir="rtl"] .unified-table-desktop .table thead th {
  text-align: right;
}

[dir="rtl"] .unified-mobile-field-value {
  text-align: right;
}

[dir="rtl"] .sidebar {
  right: -100%;
  left: auto;
}

[dir="rtl"] .sidebar.show {
  right: 0;
}

/* Focus states for accessibility */
.unified-btn-mobile:focus,
.btn:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

.unified-table-desktop .table tbody tr:focus-within {
  background: rgba(99, 102, 241, 0.1);
  outline: 2px solid #6366f1;
  outline-offset: -2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .unified-card-item,
  .unified-mobile-card {
    border: 2px solid #000;
  }

  .unified-status-badge {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .unified-card-item,
  .unified-mobile-card,
  .unified-table-desktop .table tbody tr,
  .unified-btn-mobile {
    transition: none;
  }

  .unified-card-item:hover,
  .unified-mobile-card:hover,
  .unified-table-desktop .table tbody tr:hover {
    transform: none;
  }
}
