/**
 * Unified Responsive Tables JavaScript
 * Handles responsive table transformations and interactions
 * Supports Arabic RTL layout with IBM Plex Sans Arabic font
 */

class UnifiedResponsiveTables {
    constructor() {
        this.init();
        this.bindEvents();
    }

    init() {
        // Initialize responsive behavior on page load
        this.handleResize();
        
        // Initialize sidebar functionality
        this.initSidebar();
        
        // Initialize table interactions
        this.initTableInteractions();
        
        console.log('Unified Responsive Tables initialized');
    }

    bindEvents() {
        // Handle window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Handle sidebar toggle
        document.addEventListener('click', (e) => {
            if (e.target.closest('.navbar-toggler')) {
                e.preventDefault();
                this.toggleSidebar();
            }
            
            // Close sidebar when clicking overlay
            if (e.target.classList.contains('sidebar-overlay')) {
                this.closeSidebar();
            }
        });

        // Handle keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSidebar();
            }
        });

        // Handle table row clicks for mobile
        document.addEventListener('click', (e) => {
            const mobileCard = e.target.closest('.unified-mobile-card');
            if (mobileCard && !e.target.closest('.unified-mobile-actions')) {
                this.handleMobileCardClick(mobileCard);
            }
        });
    }

    handleResize() {
        const width = window.innerWidth;
        
        // Update responsive classes based on screen size
        this.updateResponsiveDisplay(width);
        
        // Handle sidebar state
        this.handleSidebarResize(width);
        
        // Debug logging
        this.logResponsiveState(width);
    }

    updateResponsiveDisplay(width) {
        const tables = document.querySelectorAll('.unified-responsive-table');
        
        tables.forEach(table => {
            const desktop = table.querySelector('.unified-table-desktop');
            const tablet = table.querySelector('.unified-cards-tablet');
            const mobile = table.querySelector('.unified-cards-mobile');
            
            if (width >= 992) {
                // Desktop: Show table, hide cards
                if (desktop) desktop.style.display = 'block';
                if (tablet) tablet.style.display = 'none';
                if (mobile) mobile.style.display = 'none';
            } else if (width >= 768) {
                // Tablet: Show tablet cards, hide table and mobile cards
                if (desktop) desktop.style.display = 'none';
                if (tablet) tablet.style.display = 'grid';
                if (mobile) mobile.style.display = 'none';
            } else {
                // Mobile: Show mobile cards, hide table and tablet cards
                if (desktop) desktop.style.display = 'none';
                if (tablet) tablet.style.display = 'none';
                if (mobile) mobile.style.display = 'flex';
            }
        });
    }

    handleSidebarResize(width) {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (!sidebar) return;
        
        if (width >= 992) {
            // Desktop: Ensure sidebar is visible and overlay is hidden
            sidebar.classList.remove('show');
            if (overlay) overlay.classList.remove('show');
            document.body.style.overflow = '';
        } else {
            // Tablet/Mobile: Ensure sidebar is hidden by default
            if (!sidebar.classList.contains('show')) {
                if (overlay) overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
    }

    initSidebar() {
        // Create overlay if it doesn't exist
        if (!document.querySelector('.sidebar-overlay')) {
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);
        }

        // Create hamburger menu if it doesn't exist
        this.createHamburgerMenu();
    }

    createHamburgerMenu() {
        const existingToggler = document.querySelector('.navbar-toggler');
        if (existingToggler) return;

        const toggler = document.createElement('button');
        toggler.className = 'navbar-toggler';
        toggler.setAttribute('aria-label', 'Toggle navigation');
        toggler.innerHTML = `
            <div class="navbar-toggler-icon">
                <span></span>
                <span></span>
                <span></span>
            </div>
        `;

        // Insert at the beginning of main content or header
        const mainContent = document.querySelector('.main-content');
        const header = document.querySelector('.card-header');
        
        if (mainContent) {
            mainContent.insertBefore(toggler, mainContent.firstChild);
        } else if (header) {
            header.insertBefore(toggler, header.firstChild);
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        const toggler = document.querySelector('.navbar-toggler');
        
        if (!sidebar) return;
        
        const isOpen = sidebar.classList.contains('show');
        
        if (isOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
        
        // Toggle hamburger animation
        if (toggler) {
            toggler.classList.toggle('active', !isOpen);
        }
    }

    openSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebar) sidebar.classList.add('show');
        if (overlay) overlay.classList.add('show');
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Focus management for accessibility
        const firstFocusable = sidebar?.querySelector('a, button, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            setTimeout(() => firstFocusable.focus(), 300);
        }
    }

    closeSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        const toggler = document.querySelector('.navbar-toggler');
        
        if (sidebar) sidebar.classList.remove('show');
        if (overlay) overlay.classList.remove('show');
        if (toggler) toggler.classList.remove('active');
        
        // Restore body scroll
        document.body.style.overflow = '';
    }

    initTableInteractions() {
        // Add hover effects and click handlers
        this.initTableRowInteractions();
        this.initCardInteractions();
        this.initButtonInteractions();
    }

    initTableRowInteractions() {
        const tableRows = document.querySelectorAll('.unified-table-desktop tbody tr');
        
        tableRows.forEach(row => {
            row.addEventListener('click', (e) => {
                // Don't trigger if clicking on buttons or links
                if (e.target.closest('a, button, .btn')) return;
                
                // Add selection state or navigate to detail view
                this.handleTableRowClick(row);
            });
        });
    }

    initCardInteractions() {
        const cards = document.querySelectorAll('.unified-card-item, .unified-mobile-card');
        
        cards.forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't trigger if clicking on action buttons
                if (e.target.closest('.unified-card-actions, .unified-mobile-actions')) return;
                
                this.handleCardClick(card);
            });
        });
    }

    initButtonInteractions() {
        const buttons = document.querySelectorAll('.unified-btn-mobile');
        
        buttons.forEach(button => {
            // Add ripple effect on click
            button.addEventListener('click', (e) => {
                this.addRippleEffect(button, e);
            });
        });
    }

    handleTableRowClick(row) {
        // Remove previous selections
        document.querySelectorAll('.unified-table-desktop tbody tr.selected')
            .forEach(r => r.classList.remove('selected'));
        
        // Add selection to current row
        row.classList.add('selected');
        
        // You can add navigation logic here
        console.log('Table row clicked:', row);
    }

    handleCardClick(card) {
        // Add visual feedback
        card.style.transform = 'scale(0.98)';
        setTimeout(() => {
            card.style.transform = '';
        }, 150);
        
        console.log('Card clicked:', card);
    }

    handleMobileCardClick(card) {
        // Mobile-specific card interaction
        this.handleCardClick(card);
    }

    addRippleEffect(button, event) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }

    logResponsiveState(width) {
        const breakpoint = width >= 992 ? 'Desktop' : width >= 768 ? 'Tablet' : 'Mobile';
        console.log(`Responsive state: ${breakpoint} (${width}px)`);
    }

    // Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Add ripple animation CSS
const rippleCSS = `
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new UnifiedResponsiveTables();
    });
} else {
    new UnifiedResponsiveTables();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedResponsiveTables;
}
