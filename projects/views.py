from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import Project


@login_required
def project_list(request):
    """قائمة المشاريع"""
    projects = Project.objects.all().order_by('-created_at')

    # Table configuration for unified responsive table
    projects_table_config = {
        'title': 'قائمة المشاريع',
        'icon': 'fas fa-project-diagram',
        'columns': [
            {
                'key': 'name',
                'label': 'اسم المشروع',
                'type': 'text',
                'subtitle_field': 'description',
            },
            {
                'key': 'client.name',
                'label': 'العميل',
                'type': 'text',
            },
            {
                'key': 'get_type_display_arabic',
                'label': 'النوع',
                'type': 'text',
            },
            {
                'key': 'get_status_display_arabic',
                'label': 'الحالة',
                'type': 'status',
                'status_map': {
                    'planning': 'warning',
                    'in_progress': 'info',
                    'testing': 'info',
                    'completed': 'success',
                    'cancelled': 'danger',
                },
            },
            {
                'key': 'progress',
                'label': 'التقدم',
                'type': 'progress',
            },
            {
                'key': 'deadline',
                'label': 'الموعد النهائي',
                'type': 'date',
                'date_format': 'Y/m/d',
            },
        ],
        'actions': [
            {
                'label': 'عرض',
                'icon': 'fas fa-eye',
                'url_name': 'projects:detail',
                'class': 'unified-btn-primary',
                'show_label': True,
            },
            {
                'label': 'تعديل',
                'icon': 'fas fa-edit',
                'url_name': 'projects:edit',
                'class': 'unified-btn-secondary',
                'show_label': False,
            },
        ]
    }

    context = {
        'projects': projects,
        'projects_table_config': projects_table_config,
    }

    return render(request, 'projects/list.html', context)


@login_required
def project_detail(request, pk):
    """تفاصيل المشروع"""
    project = get_object_or_404(Project, pk=pk)
    return render(request, 'projects/detail.html', {'project': project})


@login_required
def project_create(request):
    """إنشاء مشروع جديد"""
    # TODO: Implement project creation form
    messages.info(request, 'صفحة إنشاء المشاريع قيد التطوير')
    return redirect('projects:list')


@login_required
def project_edit(request, pk):
    """تعديل المشروع"""
    project = get_object_or_404(Project, pk=pk)
    # TODO: Implement project edit form
    messages.info(request, 'صفحة تعديل المشاريع قيد التطوير')
    return redirect('projects:detail', pk=pk)


@login_required
def project_delete(request, pk):
    """حذف المشروع"""
    project = get_object_or_404(Project, pk=pk)
    # TODO: Implement project deletion
    messages.info(request, 'وظيفة حذف المشاريع قيد التطوير')
    return redirect('projects:list')
