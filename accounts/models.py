from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """نموذج المستخدم المخصص"""

    ROLE_CHOICES = [
        ('founder', 'مؤسس'),
        ('admin', 'مدير'),
        ('project_manager', 'مدير مشروع'),
        ('developer', 'مطور'),
        ('designer', 'مصمم'),
        ('sales', 'مبيعات'),
        ('accountant', 'محاسب'),
        ('hr', 'موارد بشرية'),
    ]

    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='developer',
        verbose_name='الدور'
    )

    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='رقم الهاتف'
    )

    avatar = models.ImageField(
        upload_to='avatars/',
        blank=True,
        null=True,
        verbose_name='الصورة الشخصية'
    )

    department = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='القسم'
    )

    hire_date = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ التوظيف'
    )

    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='الراتب'
    )

    is_active_employee = models.BooleanField(
        default=True,
        verbose_name='موظف نشط'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'

    def __str__(self):
        return f"{self.first_name} {self.last_name}" if self.first_name else self.username

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    def get_role_display_arabic(self):
        role_dict = dict(self.ROLE_CHOICES)
        return role_dict.get(self.role, self.role)

    def get_status_display(self):
        return 'نشط' if self.is_active else 'غير نشط'
