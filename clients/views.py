from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import Client


@login_required
def client_list(request):
    """قائمة العملاء"""
    clients = Client.objects.all().order_by('-created_at')

    # Table configuration for unified responsive table
    clients_table_config = {
        'title': 'قائمة العملاء',
        'icon': 'fas fa-users',
        'columns': [
            {
                'key': 'name',
                'label': 'العميل',
                'type': 'avatar',
                'avatar_field': 'name',
                'subtitle_field': 'company_name',
            },
            {
                'key': 'company_name',
                'label': 'الشركة',
                'type': 'text',
            },
            {
                'key': 'email',
                'label': 'البريد الإلكتروني',
                'type': 'text',
            },
            {
                'key': 'phone',
                'label': 'الهاتف',
                'type': 'text',
            },
            {
                'key': 'created_at',
                'label': 'تاريخ الانضمام',
                'type': 'date',
                'date_format': 'Y/m/d',
            },
        ],
        'actions': [
            {
                'label': 'عرض',
                'icon': 'fas fa-eye',
                'url_name': 'clients:detail',
                'class': 'unified-btn-primary',
                'show_label': True,
            },
            {
                'label': 'تعديل',
                'icon': 'fas fa-edit',
                'url_name': 'clients:edit',
                'class': 'unified-btn-secondary',
                'show_label': False,
            },
        ]
    }

    context = {
        'clients': clients,
        'clients_table_config': clients_table_config,
    }

    return render(request, 'clients/list.html', context)


@login_required
def client_detail(request, pk):
    """تفاصيل العميل"""
    client = get_object_or_404(Client, pk=pk)
    return render(request, 'clients/detail.html', {'client': client})


@login_required
def client_create(request):
    """إنشاء عميل جديد"""
    messages.info(request, 'صفحة إنشاء العملاء قيد التطوير')
    return redirect('clients:list')


@login_required
def client_edit(request, pk):
    """تعديل العميل"""
    messages.info(request, 'صفحة تعديل العملاء قيد التطوير')
    return redirect('clients:detail', pk=pk)


@login_required
def client_delete(request, pk):
    """حذف العميل"""
    messages.info(request, 'وظيفة حذف العملاء قيد التطوير')
    return redirect('clients:list')
