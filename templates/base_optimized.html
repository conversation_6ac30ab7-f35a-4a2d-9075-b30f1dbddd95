<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      {% block title %}ليال - نظام إدارة الشركات البرمجية{% endblock %}
    </title>

    <!-- Bootstrap RTL CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css"
      rel="stylesheet"
    />

    <!-- IBM Plex Sans Arabic Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Font Awesome Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Optimized CSS -->
    <style>
      :root {
        /* Simplified color palette */
        --primary: #6366f1;
        --primary-dark: #4f46e5;
        --secondary: #8b5cf6;
        --accent: #f59e0b;
        --success: #10b981;
        --danger: #ef4444;
        --dark: #1f2937;
        --light: #f8fafc;
        --border: #e5e7eb;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --radius: 12px;
        --radius-lg: 16px;
      }

      * {
        font-family: "IBM Plex Sans Arabic", sans-serif;
      }

      /* Optimized background - single gradient instead of multiple layers */
      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: var(--text-primary);
        /* Remove complex animations */
      }

      /* Alternative light theme for better performance */
      body.light-theme {
        background: #f8fafc;
      }

      body.light-theme .sidebar {
        background: white;
        border-right: 1px solid var(--border);
      }

      /* Simplified sidebar - solid background instead of glass */
      .sidebar {
        background: rgba(255, 255, 255, 0.95);
        min-height: 100vh;
        box-shadow: var(--shadow-lg);
        border-left: 3px solid var(--primary);
        /* Remove backdrop-filter for better performance */
      }

      .sidebar .nav-link {
        color: var(--text-primary);
        padding: 12px 20px;
        margin: 4px 12px;
        border-radius: var(--radius);
        transition: all 0.2s ease; /* Reduced transition time */
        font-weight: 500;
      }

      .sidebar .nav-link:hover,
      .sidebar .nav-link.active {
        background: var(--primary);
        color: white;
        transform: translateX(-3px); /* Reduced transform */
      }

      .sidebar .nav-link i {
        margin-left: 10px;
        width: 18px;
        text-align: center;
      }

      /* Optimized main content */
      .main-content {
        background: white;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        margin: 20px;
        padding: 30px;
        /* Remove complex pseudo-elements */
      }

      /* Simplified cards */
      .card {
        background: white;
        border: 1px solid var(--border);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
      }

      .card:hover {
        transform: translateY(-2px); /* Reduced movement */
        box-shadow: var(--shadow-lg);
      }

      .card-header {
        background: var(--primary);
        color: white;
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        padding: 20px;
        font-weight: 600;
        border: none;
      }

      /* Optimized buttons */
      .btn-primary {
        background: var(--primary);
        border: none;
        border-radius: var(--radius);
        padding: 10px 24px;
        font-weight: 600;
        transition: all 0.2s ease;
      }

      .btn-primary:hover {
        background: var(--primary-dark);
        transform: translateY(-1px);
        box-shadow: var(--shadow);
      }

      /* Simplified stats cards */
      .stats-card {
        background: white;
        border-radius: var(--radius-lg);
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: var(--shadow);
        border-left: 4px solid var(--primary);
        transition: transform 0.2s ease;
      }

      .stats-card:hover {
        transform: translateY(-2px);
      }

      .stats-card.success {
        border-left-color: var(--success);
      }

      .stats-card.warning {
        border-left-color: var(--accent);
      }

      .stats-card.danger {
        border-left-color: var(--danger);
      }

      .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary);
        margin-bottom: 8px;
      }

      .stats-label {
        color: var(--text-secondary);
        font-weight: 500;
      }

      /* Optimized page header */
      .page-header {
        background: var(--primary);
        color: white;
        padding: 30px;
        border-radius: var(--radius-lg);
        margin-bottom: 30px;
        box-shadow: var(--shadow);
      }

      .page-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
      }

      /* Simplified table */
      .table {
        border-radius: var(--radius);
        overflow: hidden;
        box-shadow: var(--shadow);
      }

      .table thead th {
        background: var(--primary);
        color: white;
        border: none;
        font-weight: 600;
        padding: 16px;
      }

      .table tbody td {
        padding: 16px;
        border-color: var(--border);
        vertical-align: middle;
      }

      .table tbody tr:hover {
        background-color: #f8fafc;
      }

      /* Optimized forms */
      .form-control,
      .form-select {
        border: 2px solid var(--border);
        border-radius: var(--radius);
        padding: 12px 16px;
        transition: border-color 0.2s ease;
      }

      .form-control:focus,
      .form-select:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      }

      /* Simplified alerts */
      .alert {
        border-radius: var(--radius);
        border: none;
        padding: 16px 20px;
      }

      /* Optimized dropdown */
      .dropdown-menu {
        border-radius: var(--radius);
        border: 1px solid var(--border);
        box-shadow: var(--shadow-lg);
        padding: 8px 0;
      }

      .dropdown-item {
        padding: 10px 20px;
        transition: background-color 0.2s ease;
      }

      .dropdown-item:hover {
        background-color: var(--primary);
        color: white;
      }

      /* User avatar - simplified */
      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
      }

      /* Progress bars */
      .progress {
        height: 8px;
        border-radius: 4px;
        background-color: var(--border);
      }

      .progress-bar {
        background: var(--primary);
        border-radius: 4px;
      }

      /* Badges */
      .badge {
        border-radius: 20px;
        padding: 6px 12px;
        font-weight: 500;
      }

      .badge.bg-success {
        background: var(--success) !important;
      }

      .badge.bg-warning {
        background: var(--accent) !important;
      }

      .badge.bg-danger {
        background: var(--danger) !important;
      }

      /* ===== RESPONSIVE DESIGN SYSTEM ===== */

      /* Default state - hide all responsive elements */
      .responsive-table {
        display: block;
      }

      .responsive-cards {
        display: none;
      }

      .responsive-cards.tablet-cards {
        display: none;
      }

      .responsive-cards.mobile-cards {
        display: none;
      }

      /* Desktop First (1200px+) - Keep current layouts */
      @media (min-width: 1200px) {
        .responsive-table {
          display: block !important;
        }

        .responsive-cards {
          display: none !important;
        }

        .responsive-cards.tablet-cards {
          display: none !important;
        }

        .responsive-cards.mobile-cards {
          display: none !important;
        }

        .sidebar {
          position: relative;
          transform: translateX(0);
        }

        .main-content {
          margin: 20px;
          padding: 30px;
        }
      }

      /* Tablet Layout (768px - 1199px) */
      @media (min-width: 768px) and (max-width: 1199px) {
        .responsive-table {
          display: none !important;
        }

        .responsive-cards {
          display: block !important;
        }

        .responsive-cards.tablet-cards {
          display: block !important;
        }

        .responsive-cards.mobile-cards {
          display: none !important;
        }

        .responsive-cards .row {
          margin: -10px;
        }

        .responsive-cards .col-md-6 {
          padding: 10px;
        }

        .sidebar {
          position: fixed;
          top: 0;
          right: -320px;
          width: 320px;
          height: 100vh;
          z-index: 1050;
          transition: right 0.3s ease;
          box-shadow: var(--shadow-2xl);
        }

        .sidebar.show {
          right: 0;
        }

        .sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          z-index: 1040;
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
          opacity: 1;
          visibility: visible;
        }

        .main-content {
          margin: 15px;
          padding: 25px;
        }

        .page-title {
          font-size: 1.75rem;
        }

        .card-responsive {
          margin-bottom: 20px;
          transition: transform 0.2s ease;
        }

        .card-responsive:hover {
          transform: translateY(-2px);
        }

        /* Tablet-optimized buttons */
        .btn {
          min-height: 44px;
          padding: 12px 20px;
        }

        .btn-sm {
          min-height: 40px;
          padding: 10px 16px;
        }
      }

      /* Mobile Layout (below 768px) */
      @media (max-width: 767px) {
        .responsive-table {
          display: none !important;
        }

        .responsive-cards {
          display: none !important;
        }

        .responsive-cards.mobile-cards {
          display: block !important;
        }

        .responsive-cards.tablet-cards {
          display: none !important;
        }

        .sidebar {
          position: fixed;
          top: 0;
          right: -100%;
          width: 100%;
          height: 100vh;
          z-index: 1050;
          transition: right 0.3s ease;
          box-shadow: none;
        }

        .sidebar.show {
          right: 0;
        }

        .sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.6);
          z-index: 1040;
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
          opacity: 1;
          visibility: visible;
        }

        .main-content {
          margin: 10px;
          padding: 15px;
          border-radius: var(--radius);
        }

        .page-title {
          font-size: 1.5rem;
          line-height: 1.3;
        }

        .page-header {
          padding: 20px 15px;
          margin-bottom: 20px;
        }

        .stats-card {
          margin-bottom: 15px;
          padding: 20px;
        }

        .stats-number {
          font-size: 2rem;
        }

        /* Mobile-optimized cards */
        .card-mobile {
          margin-bottom: 15px;
          border-radius: var(--radius);
          box-shadow: var(--shadow);
        }

        .card-mobile .card-body {
          padding: 20px;
        }

        .card-mobile .card-header {
          padding: 15px 20px;
          font-size: 1rem;
        }

        /* Mobile-optimized buttons - 44px minimum touch target */
        .btn {
          min-height: 44px;
          padding: 12px 24px;
          font-size: 1rem;
          border-radius: var(--radius);
        }

        .btn-sm {
          min-height: 44px;
          padding: 12px 20px;
          font-size: 0.9rem;
        }

        .btn-group .btn {
          margin: 2px;
        }

        /* Mobile navigation improvements */
        .mobile-nav-toggle {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 1060;
          background: var(--primary);
          color: white;
          border: none;
          border-radius: 50%;
          width: 56px;
          height: 56px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: var(--shadow-lg);
          transition: all 0.2s ease;
        }

        .mobile-nav-toggle:hover {
          background: var(--primary-dark);
          transform: scale(1.05);
        }

        .mobile-nav-toggle.active {
          background: var(--danger);
        }

        /* Mobile-specific typography */
        .mobile-text-sm {
          font-size: 0.875rem;
          line-height: 1.4;
        }

        .mobile-text-xs {
          font-size: 0.75rem;
          line-height: 1.3;
        }

        /* Mobile form improvements */
        .form-control,
        .form-select {
          min-height: 44px;
          font-size: 1rem;
        }

        /* Mobile table alternative - stacked cards */
        .mobile-card-item {
          background: white;
          border-radius: var(--radius);
          padding: 20px;
          margin-bottom: 15px;
          box-shadow: var(--shadow);
          border-left: 4px solid var(--primary);
        }

        .mobile-card-item .item-header {
          display: flex;
          justify-content: between;
          align-items: flex-start;
          margin-bottom: 15px;
        }

        .mobile-card-item .item-title {
          font-weight: 600;
          font-size: 1.1rem;
          color: var(--text-primary);
          margin-bottom: 5px;
        }

        .mobile-card-item .item-subtitle {
          font-size: 0.9rem;
          color: var(--text-secondary);
        }

        .mobile-card-item .item-details {
          margin: 15px 0;
        }

        .mobile-card-item .item-detail {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid var(--border);
        }

        .mobile-card-item .item-detail:last-child {
          border-bottom: none;
        }

        .mobile-card-item .item-detail .label {
          font-weight: 500;
          color: var(--text-secondary);
          font-size: 0.9rem;
        }

        .mobile-card-item .item-detail .value {
          font-weight: 500;
          color: var(--text-primary);
        }

        .mobile-card-item .item-actions {
          display: flex;
          gap: 10px;
          margin-top: 15px;
          padding-top: 15px;
          border-top: 1px solid var(--border);
        }

        .mobile-card-item .item-actions .btn {
          flex: 1;
          text-align: center;
        }
      }

      /* Reduced motion for accessibility */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }

      /* Loading optimization */
      .loading {
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .loaded {
        opacity: 1;
      }

      /* Focus styles for accessibility */
      .btn:focus,
      .nav-link:focus,
      .form-control:focus {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
      }

      /* Print styles */
      @media print {
        .sidebar,
        .btn,
        .dropdown {
          display: none !important;
        }

        .main-content {
          margin: 0;
          box-shadow: none;
        }
      }
    </style>

    {% block extra_css %}{% endblock %}
  </head>
  <body class="loading">
    {% block content %}{% endblock %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Optimized JavaScript -->
    <script>
      // Optimized loading and responsive setup
      document.addEventListener("DOMContentLoaded", function () {
        document.body.classList.add("loaded");

        // Add event listeners for responsive functionality
        const overlay = document.querySelector(".sidebar-overlay");
        if (overlay) {
          overlay.addEventListener("click", closeSidebar);
        }

        // Handle window resize
        window.addEventListener("resize", handleResize);

        // Initialize responsive state
        handleResize();
      });

      // Enhanced responsive sidebar toggle
      function toggleSidebar() {
        const sidebar = document.querySelector(".sidebar");
        const overlay = document.querySelector(".sidebar-overlay");
        const toggle = document.querySelector(".mobile-nav-toggle");

        if (sidebar) {
          sidebar.classList.toggle("show");

          // Handle overlay for tablet and mobile
          if (overlay) {
            overlay.classList.toggle("show");
          }

          // Update toggle button state
          if (toggle) {
            toggle.classList.toggle("active");
          }

          // Prevent body scroll when sidebar is open on mobile
          if (window.innerWidth < 768) {
            document.body.style.overflow = sidebar.classList.contains("show")
              ? "hidden"
              : "";
          }
        }
      }

      // Close sidebar when clicking overlay
      function closeSidebar() {
        const sidebar = document.querySelector(".sidebar");
        const overlay = document.querySelector(".sidebar-overlay");
        const toggle = document.querySelector(".mobile-nav-toggle");

        if (sidebar) {
          sidebar.classList.remove("show");

          if (overlay) {
            overlay.classList.remove("show");
          }

          if (toggle) {
            toggle.classList.remove("active");
          }

          document.body.style.overflow = "";
        }
      }

      // Handle responsive behavior on window resize
      function handleResize() {
        const sidebar = document.querySelector(".sidebar");
        const overlay = document.querySelector(".sidebar-overlay");

        if (window.innerWidth >= 1200) {
          // Desktop: ensure sidebar is visible and overlay is hidden
          if (sidebar) {
            sidebar.classList.remove("show");
          }
          if (overlay) {
            overlay.classList.remove("show");
          }
          document.body.style.overflow = "";
        }

        // Debug responsive layout
        console.log("Window width:", window.innerWidth);
        console.log(
          "Responsive table display:",
          getComputedStyle(document.querySelector(".responsive-table") || {})
            .display
        );
        console.log(
          "Tablet cards display:",
          getComputedStyle(
            document.querySelector(".responsive-cards.tablet-cards") || {}
          ).display
        );
        console.log(
          "Mobile cards display:",
          getComputedStyle(
            document.querySelector(".responsive-cards.mobile-cards") || {}
          ).display
        );
      }

      // Optimized alert auto-hide
      setTimeout(() => {
        document
          .querySelectorAll(".alert-success, .alert-info")
          .forEach((alert) => {
            alert.style.transition = "opacity 0.3s ease";
            alert.style.opacity = "0";
            setTimeout(() => alert.remove(), 300);
          });
      }, 4000);

      // Performance monitoring (development only)
      if (window.performance && console.time) {
        console.time("Page Load");
        window.addEventListener("load", () => {
          console.timeEnd("Page Load");
        });
      }
    </script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
