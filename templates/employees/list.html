{% extends 'base_optimized.html' %} {% block title %}الموظفون - ليال{% endblock
%} {% block content %}
<!-- Sidebar Overlay for Mobile/Tablet -->
<div class="sidebar-overlay" onclick="closeSidebar()"></div>

<!-- Mobile Navigation Toggle -->
<button class="mobile-nav-toggle d-lg-none" onclick="toggleSidebar()">
  <i class="fas fa-bars"></i>
</button>

<div class="container-fluid">
  <div class="row">
    <!-- Responsive Sidebar -->
    <div class="col-lg-2 px-0">
      <div class="sidebar">
        <div class="p-4 text-center border-bottom">
          <h3 class="mb-3" style="color: var(--primary); font-weight: 800">
            <i class="fas fa-crown me-2"></i>ليال
          </h3>
          <small class="text-muted">نظام إدارة الشركات</small>
        </div>

        <nav class="nav flex-column p-2">
          <a class="nav-link" href="{% url 'core:dashboard' %}">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم
          </a>
          <a class="nav-link" href="{% url 'projects:list' %}">
            <i class="fas fa-project-diagram"></i>
            المشاريع
          </a>
          <a class="nav-link" href="{% url 'clients:list' %}">
            <i class="fas fa-users"></i>
            العملاء
          </a>
          <a class="nav-link active" href="{% url 'employees:list' %}">
            <i class="fas fa-user-tie"></i>
            الموظفون
          </a>
        </nav>

        <!-- User Profile Section -->
        <div class="mt-auto p-3 border-top">
          <div class="d-flex align-items-center">
            <div class="user-avatar me-3">
              {{ user.first_name.0|default:user.username.0 }}
            </div>
            <div class="flex-grow-1">
              <div class="fw-bold small">
                {{ user.get_full_name|default:user.username }}
              </div>
              <div class="text-muted small">
                {{ user.get_role_display_arabic }}
              </div>
            </div>
            <div class="dropdown">
              <button
                class="btn btn-link text-muted p-0"
                type="button"
                data-bs-toggle="dropdown"
              >
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-user me-2"></i>الملف الشخصي</a
                  >
                </li>
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-cog me-2"></i>الإعدادات</a
                  >
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="{% url 'accounts:logout' %}"
                    ><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a
                  >
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="col-lg-10">
      <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="page-title">
                <i class="fas fa-user-tie me-3"></i>
                إدارة الموظفين
              </h1>
              <p class="mb-0 opacity-90">عرض وإدارة جميع موظفي الشركة</p>
            </div>
            <div class="d-none d-lg-block">
              <button class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>موظف جديد
              </button>
            </div>
          </div>
        </div>

        <!-- Employees Content -->
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0 d-flex align-items-center">
              <i class="fas fa-list me-2"></i>
              قائمة الموظفين
              <span class="badge bg-light text-dark ms-auto"
                >{{ employees|length }}</span
              >
            </h5>
          </div>
          <div class="card-body">
            {% if employees %}
            <!-- Desktop Table Layout (1200px+) -->
            <div class="responsive-table">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>الاسم</th>
                      <th>البريد الإلكتروني</th>
                      <th>الدور</th>
                      <th>تاريخ الانضمام</th>
                      <th>الحالة</th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for employee in employees %}
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="user-avatar me-3">
                            {{ employee.first_name.0|default:employee.username.0
                            }}
                          </div>
                          <div>
                            <strong
                              >{{
                              employee.get_full_name|default:employee.username
                              }}</strong
                            >
                            <br />
                            <small class="text-muted"
                              >{{ employee.username }}</small
                            >
                          </div>
                        </div>
                      </td>
                      <td>{{ employee.email }}</td>
                      <td>
                        <span class="badge bg-primary"
                          >{{ employee.get_role_display_arabic }}</span
                        >
                      </td>
                      <td>{{ employee.date_joined|date:"Y/m/d" }}</td>
                      <td>
                        {% if employee.is_active %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-danger">غير نشط</span>
                        {% endif %}
                      </td>
                      <td>
                        <div class="btn-group" role="group">
                          <a
                            href="{% url 'employees:detail' employee.pk %}"
                            class="btn btn-sm btn-primary"
                          >
                            <i class="fas fa-eye"></i>
                          </a>
                          <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Tablet Card Layout -->
            <div class="responsive-cards tablet-cards">
              <div class="row">
                {% for employee in employees %}
                <div class="col-md-6 col-xl-4">
                  <div class="card card-responsive">
                    <div class="card-body">
                      <div class="d-flex align-items-start mb-3">
                        <div class="user-avatar me-3">
                          {{ employee.first_name.0|default:employee.username.0
                          }}
                        </div>
                        <div class="flex-grow-1">
                          <h6 class="mb-1">
                            {{ employee.get_full_name|default:employee.username
                            }}
                          </h6>
                          <small class="text-muted"
                            >{{ employee.username }}</small
                          >
                        </div>
                        {% if employee.is_active %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-danger">غير نشط</span>
                        {% endif %}
                      </div>

                      <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                          <i class="fas fa-envelope text-muted me-2"></i>
                          <small>{{ employee.email }}</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                          <i class="fas fa-user-tag text-muted me-2"></i>
                          <span class="badge bg-primary"
                            >{{ employee.get_role_display_arabic }}</span
                          >
                        </div>
                        <div class="d-flex align-items-center">
                          <i class="fas fa-calendar text-muted me-2"></i>
                          <small
                            >انضم في {{ employee.date_joined|date:"Y/m/d"
                            }}</small
                          >
                        </div>
                      </div>

                      <div class="d-flex justify-content-between">
                        <a
                          href="{% url 'employees:detail' employee.pk %}"
                          class="btn btn-sm btn-primary"
                        >
                          <i class="fas fa-eye me-1"></i>عرض
                        </a>
                        <button class="btn btn-sm btn-outline-primary">
                          <i class="fas fa-edit me-1"></i>تعديل
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                {% endfor %}
              </div>
            </div>

            <!-- Mobile Card Layout (below 768px) -->
            <div class="responsive-cards mobile-cards">
              {% for employee in employees %}
              <div class="mobile-card-item">
                <div class="item-header">
                  <div class="d-flex align-items-center flex-grow-1">
                    <div class="user-avatar me-3">
                      {{ employee.first_name.0|default:employee.username.0 }}
                    </div>
                    <div>
                      <div class="item-title">
                        {{ employee.get_full_name|default:employee.username }}
                      </div>
                      <div class="item-subtitle">{{ employee.username }}</div>
                    </div>
                  </div>
                  {% if employee.is_active %}
                  <span class="badge bg-success">نشط</span>
                  {% else %}
                  <span class="badge bg-danger">غير نشط</span>
                  {% endif %}
                </div>

                <div class="item-details">
                  <div class="item-detail">
                    <span class="label">البريد الإلكتروني</span>
                    <span class="value">{{ employee.email }}</span>
                  </div>
                  <div class="item-detail">
                    <span class="label">الدور</span>
                    <span class="value">
                      <span class="badge bg-primary"
                        >{{ employee.get_role_display_arabic }}</span
                      >
                    </span>
                  </div>
                  <div class="item-detail">
                    <span class="label">تاريخ الانضمام</span>
                    <span class="value"
                      >{{ employee.date_joined|date:"Y/m/d" }}</span
                    >
                  </div>
                </div>

                <div class="item-actions">
                  <a
                    href="{% url 'employees:detail' employee.pk %}"
                    class="btn btn-primary"
                  >
                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                  </a>
                  <button class="btn btn-outline-primary">
                    <i class="fas fa-edit me-2"></i>تعديل
                  </button>
                </div>
              </div>
              {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
              <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا يوجد موظفون حالياً</h5>
              <p class="text-muted">ابدأ بإضافة موظفين جدد لفريق العمل</p>
              <button class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة موظف جديد
              </button>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
