{% extends 'base_optimized.html' %}

{% block title %}العملاء - ليال{% endblock %}

{% block content %}
<!-- Sidebar Overlay for Mobile/Tablet -->
<div class="sidebar-overlay" onclick="closeSidebar()"></div>

<!-- Mobile Navigation Toggle -->
<button class="mobile-nav-toggle d-lg-none" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<div class="container-fluid">
    <div class="row">
        <!-- Responsive Sidebar -->
        <div class="col-lg-2 px-0">
            <div class="sidebar">
                <div class="p-4 text-center border-bottom">
                    <h3 class="mb-3" style="color: var(--primary); font-weight: 800;">
                        <i class="fas fa-crown me-2"></i>ليال
                    </h3>
                    <small class="text-muted">نظام إدارة الشركات</small>
                </div>
                
                <nav class="nav flex-column p-2">
                    <a class="nav-link" href="{% url 'core:dashboard' %}">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم
                    </a>
                    <a class="nav-link" href="{% url 'projects:list' %}">
                        <i class="fas fa-project-diagram"></i>
                        المشاريع
                    </a>
                    <a class="nav-link active" href="{% url 'clients:list' %}">
                        <i class="fas fa-users"></i>
                        العملاء
                    </a>
                    <a class="nav-link" href="{% url 'employees:list' %}">
                        <i class="fas fa-user-tie"></i>
                        الموظفون
                    </a>
                </nav>
                
                <!-- User Profile Section -->
                <div class="mt-auto p-3 border-top">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-3">
                            {{ user.first_name.0|default:user.username.0 }}
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold small">{{ user.get_full_name|default:user.username }}</div>
                            <div class="text-muted small">{{ user.get_role_display_arabic }}</div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-link text-muted p-0" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-10">
            <div class="main-content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="page-title">
                                <i class="fas fa-users me-3"></i>
                                إدارة العملاء
                            </h1>
                            <p class="mb-0 opacity-90">
                                عرض وإدارة جميع عملاء الشركة
                            </p>
                        </div>
                        <div class="d-none d-lg-block">
                            <button class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>عميل جديد
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Clients Content -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-list me-2"></i>
                            قائمة العملاء
                            <span class="badge bg-light text-dark ms-auto">{{ clients|length }}</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if clients %}
                            <!-- Desktop Table Layout (1200px+) -->
                            <div class="responsive-table">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>العميل</th>
                                                <th>الشركة</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>الهاتف</th>
                                                <th>تاريخ الانضمام</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for client in clients %}
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="user-avatar me-3" style="background: var(--primary);">
                                                            {{ client.name.0 }}
                                                        </div>
                                                        <div>
                                                            <strong>{{ client.name }}</strong>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>{{ client.company_name }}</td>
                                                <td>{{ client.email }}</td>
                                                <td>{{ client.phone }}</td>
                                                <td>{{ client.created_at|date:"Y/m/d" }}</td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{% url 'clients:detail' client.pk %}" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Tablet Card Layout -->
                            <div class="responsive-cards tablet-cards">
                                <div class="row">
                                    {% for client in clients %}
                                    <div class="col-md-6 col-xl-4">
                                        <div class="card card-responsive">
                                            <div class="card-body">
                                                <div class="d-flex align-items-start mb-3">
                                                    <div class="user-avatar me-3" style="background: var(--primary);">
                                                        {{ client.name.0 }}
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">{{ client.name }}</h6>
                                                        <small class="text-muted">{{ client.company_name }}</small>
                                                    </div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="fas fa-envelope text-muted me-2"></i>
                                                        <small>{{ client.email }}</small>
                                                    </div>
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="fas fa-phone text-muted me-2"></i>
                                                        <small>{{ client.phone }}</small>
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-calendar text-muted me-2"></i>
                                                        <small>انضم في {{ client.created_at|date:"Y/m/d" }}</small>
                                                    </div>
                                                </div>
                                                
                                                <div class="d-flex justify-content-between">
                                                    <a href="{% url 'clients:detail' client.pk %}" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye me-1"></i>عرض
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit me-1"></i>تعديل
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>

                            <!-- Mobile Card Layout (below 768px) -->
                            <div class="responsive-cards mobile-cards">
                                {% for client in clients %}
                                <div class="mobile-card-item">
                                    <div class="item-header">
                                        <div class="d-flex align-items-center flex-grow-1">
                                            <div class="user-avatar me-3" style="background: var(--primary);">
                                                {{ client.name.0 }}
                                            </div>
                                            <div>
                                                <div class="item-title">{{ client.name }}</div>
                                                <div class="item-subtitle">{{ client.company_name }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="item-details">
                                        <div class="item-detail">
                                            <span class="label">البريد الإلكتروني</span>
                                            <span class="value">{{ client.email }}</span>
                                        </div>
                                        <div class="item-detail">
                                            <span class="label">الهاتف</span>
                                            <span class="value">{{ client.phone }}</span>
                                        </div>
                                        <div class="item-detail">
                                            <span class="label">تاريخ الانضمام</span>
                                            <span class="value">{{ client.created_at|date:"Y/m/d" }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="item-actions">
                                        <a href="{% url 'clients:detail' client.pk %}" class="btn btn-primary">
                                            <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                        </a>
                                        <button class="btn btn-outline-primary">
                                            <i class="fas fa-edit me-2"></i>تعديل
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد عملاء حالياً</h5>
                                <p class="text-muted">ابدأ بإضافة عملاء جدد لتنمية أعمالك</p>
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
