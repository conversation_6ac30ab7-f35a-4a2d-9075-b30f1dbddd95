{% extends 'base_optimized.html' %} {% load table_helpers %} {% block title
%}المشاريع - ليال{% endblock %} {% block content %}
<!-- Sidebar Overlay for Mobile/Tablet -->
<div class="sidebar-overlay" onclick="closeSidebar()"></div>

<!-- Mobile Navigation Toggle -->
<button class="mobile-nav-toggle d-lg-none" onclick="toggleSidebar()">
  <i class="fas fa-bars"></i>
</button>

<div class="container-fluid">
  <div class="row">
    <!-- Responsive Sidebar -->
    <div class="col-lg-2 px-0">
      <div class="sidebar">
        <div class="p-4 text-center border-bottom">
          <h3 class="mb-3" style="color: var(--primary); font-weight: 800">
            <i class="fas fa-crown me-2"></i>ليال
          </h3>
          <small class="text-muted">نظام إدارة الشركات</small>
        </div>

        <nav class="nav flex-column p-2">
          <a class="nav-link" href="{% url 'core:dashboard' %}">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم
          </a>
          <a class="nav-link active" href="{% url 'projects:list' %}">
            <i class="fas fa-project-diagram"></i>
            المشاريع
          </a>
          <a class="nav-link" href="{% url 'clients:list' %}">
            <i class="fas fa-users"></i>
            العملاء
          </a>
          <a class="nav-link" href="{% url 'employees:list' %}">
            <i class="fas fa-user-tie"></i>
            الموظفون
          </a>
        </nav>

        <!-- User Profile Section -->
        <div class="mt-auto p-3 border-top">
          <div class="d-flex align-items-center">
            <div class="user-avatar me-3">
              {{ user.first_name.0|default:user.username.0 }}
            </div>
            <div class="flex-grow-1">
              <div class="fw-bold small">
                {{ user.get_full_name|default:user.username }}
              </div>
              <div class="text-muted small">
                {{ user.get_role_display_arabic }}
              </div>
            </div>
            <div class="dropdown">
              <button
                class="btn btn-link text-muted p-0"
                type="button"
                data-bs-toggle="dropdown"
              >
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-user me-2"></i>الملف الشخصي</a
                  >
                </li>
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-cog me-2"></i>الإعدادات</a
                  >
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="{% url 'accounts:logout' %}"
                    ><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a
                  >
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="col-lg-10">
      <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="page-title">
                <i class="fas fa-project-diagram me-3"></i>
                إدارة المشاريع
              </h1>
              <p class="mb-0 opacity-90">عرض وإدارة جميع مشاريع الشركة</p>
            </div>
            <div class="d-none d-lg-block">
              <button class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>مشروع جديد
              </button>
            </div>
          </div>
        </div>

        <!-- Projects Content -->
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0 d-flex align-items-center">
              <i class="fas fa-list me-2"></i>
              قائمة المشاريع
              <span class="badge bg-light text-dark ms-auto"
                >{{ projects|length }}</span
              >
            </h5>
          </div>
          <div class="card-body">
            {% if projects %}
            <!-- Use Unified Responsive Table Component -->
            {% with table_config=projects_table_config items=projects %} {%
            include 'includes/unified_responsive_table.html' %} {% endwith %} {%
            else %}
            <div class="text-center py-5">
              <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا توجد مشاريع حالياً</h5>
              <p class="text-muted">ابدأ بإنشاء مشروع جديد لتنمية أعمالك</p>
              <button class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إنشاء مشروع جديد
              </button>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
