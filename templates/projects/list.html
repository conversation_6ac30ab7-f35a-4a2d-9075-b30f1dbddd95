{% extends 'base_optimized.html' %} {% block title %}المشاريع - ليال{% endblock
%} {% block content %}
<!-- Sidebar Overlay for Mobile/Tablet -->
<div class="sidebar-overlay" onclick="closeSidebar()"></div>

<!-- Mobile Navigation Toggle -->
<button class="mobile-nav-toggle d-lg-none" onclick="toggleSidebar()">
  <i class="fas fa-bars"></i>
</button>

<div class="container-fluid">
  <div class="row">
    <!-- Responsive Sidebar -->
    <div class="col-lg-2 px-0">
      <div class="sidebar">
        <div class="p-4 text-center border-bottom">
          <h3 class="mb-3" style="color: var(--primary); font-weight: 800">
            <i class="fas fa-crown me-2"></i>ليال
          </h3>
          <small class="text-muted">نظام إدارة الشركات</small>
        </div>

        <nav class="nav flex-column p-2">
          <a class="nav-link" href="{% url 'core:dashboard' %}">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم
          </a>
          <a class="nav-link active" href="{% url 'projects:list' %}">
            <i class="fas fa-project-diagram"></i>
            المشاريع
          </a>
          <a class="nav-link" href="{% url 'clients:list' %}">
            <i class="fas fa-users"></i>
            العملاء
          </a>
          <a class="nav-link" href="{% url 'employees:list' %}">
            <i class="fas fa-user-tie"></i>
            الموظفون
          </a>
        </nav>

        <!-- User Profile Section -->
        <div class="mt-auto p-3 border-top">
          <div class="d-flex align-items-center">
            <div class="user-avatar me-3">
              {{ user.first_name.0|default:user.username.0 }}
            </div>
            <div class="flex-grow-1">
              <div class="fw-bold small">
                {{ user.get_full_name|default:user.username }}
              </div>
              <div class="text-muted small">
                {{ user.get_role_display_arabic }}
              </div>
            </div>
            <div class="dropdown">
              <button
                class="btn btn-link text-muted p-0"
                type="button"
                data-bs-toggle="dropdown"
              >
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-user me-2"></i>الملف الشخصي</a
                  >
                </li>
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-cog me-2"></i>الإعدادات</a
                  >
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="{% url 'accounts:logout' %}"
                    ><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a
                  >
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="col-lg-10">
      <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="page-title">
                <i class="fas fa-project-diagram me-3"></i>
                إدارة المشاريع
              </h1>
              <p class="mb-0 opacity-90">عرض وإدارة جميع مشاريع الشركة</p>
            </div>
            <div class="d-none d-lg-block">
              <button class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>مشروع جديد
              </button>
            </div>
          </div>
        </div>

        <!-- Projects Content -->
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0 d-flex align-items-center">
              <i class="fas fa-list me-2"></i>
              قائمة المشاريع
              <span class="badge bg-light text-dark ms-auto"
                >{{ projects|length }}</span
              >
            </h5>
          </div>
          <div class="card-body">
            {% if projects %}
            <!-- Desktop Table Layout (1200px+) -->
            <div class="responsive-table">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>اسم المشروع</th>
                      <th>العميل</th>
                      <th>النوع</th>
                      <th>الحالة</th>
                      <th>التقدم</th>
                      <th>الموعد النهائي</th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for project in projects %}
                    <tr>
                      <td>
                        <div>
                          <strong>{{ project.name }}</strong>
                          <br />
                          <small class="text-muted"
                            >{{ project.description|truncatechars:50 }}</small
                          >
                        </div>
                      </td>
                      <td>{{ project.client.name }}</td>
                      <td>{{ project.get_type_display_arabic }}</td>
                      <td>
                        <span class="badge bg-success">
                          {{ project.get_status_display_arabic }}
                        </span>
                      </td>
                      <td>
                        <div class="progress mb-1" style="height: 8px">
                          <div
                            class="progress-bar"
                            style="width: {{ project.progress }}%;"
                          ></div>
                        </div>
                        <small class="text-muted"
                          >{{ project.progress }}%</small
                        >
                      </td>
                      <td>{{ project.deadline|date:"Y/m/d" }}</td>
                      <td>
                        <div class="btn-group" role="group">
                          <a
                            href="{% url 'projects:detail' project.pk %}"
                            class="btn btn-sm btn-primary"
                          >
                            <i class="fas fa-eye"></i>
                          </a>
                          <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Tablet Card Layout (768px - 1199px) -->
            <div class="responsive-cards tablet-cards">
              <div class="row">
                {% for project in projects %}
                <div class="col-md-6 col-xl-4">
                  <div class="card card-responsive">
                    <div class="card-body">
                      <div
                        class="d-flex justify-content-between align-items-start mb-3"
                      >
                        <div>
                          <h6 class="card-title mb-1">{{ project.name }}</h6>
                          <small class="text-muted"
                            >{{ project.client.name }}</small
                          >
                        </div>
                        <span class="badge bg-success"
                          >{{ project.get_status_display_arabic }}</span
                        >
                      </div>

                      <p class="card-text text-muted small mb-3">
                        {{ project.description|truncatechars:80 }}
                      </p>

                      <div class="mb-3">
                        <div
                          class="d-flex justify-content-between align-items-center mb-2"
                        >
                          <small class="text-muted">التقدم</small>
                          <small class="fw-bold">{{ project.progress }}%</small>
                        </div>
                        <div class="progress" style="height: 6px">
                          <div
                            class="progress-bar"
                            style="width: {{ project.progress }}%;"
                          ></div>
                        </div>
                      </div>

                      <div
                        class="d-flex justify-content-between align-items-center"
                      >
                        <small class="text-muted">
                          <i class="fas fa-calendar me-1"></i>
                          {{ project.deadline|date:"Y/m/d" }}
                        </small>
                        <div class="btn-group">
                          <a
                            href="{% url 'projects:detail' project.pk %}"
                            class="btn btn-sm btn-primary"
                          >
                            <i class="fas fa-eye"></i>
                          </a>
                          <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                {% endfor %}
              </div>
            </div>

            <!-- Mobile Card Layout (below 768px) -->
            <div class="responsive-cards mobile-cards">
              {% for project in projects %}
              <div class="mobile-card-item">
                <div class="item-header">
                  <div class="flex-grow-1">
                    <div class="item-title">{{ project.name }}</div>
                    <div class="item-subtitle">{{ project.client.name }}</div>
                  </div>
                  <span class="badge bg-success"
                    >{{ project.get_status_display_arabic }}</span
                  >
                </div>

                <div class="item-details">
                  <div class="item-detail">
                    <span class="label">النوع</span>
                    <span class="value"
                      >{{ project.get_type_display_arabic }}</span
                    >
                  </div>
                  <div class="item-detail">
                    <span class="label">التقدم</span>
                    <span class="value">
                      {{ project.progress }}%
                      <div
                        class="progress mt-1"
                        style="height: 4px; width: 60px"
                      >
                        <div
                          class="progress-bar"
                          style="width: {{ project.progress }}%;"
                        ></div>
                      </div>
                    </span>
                  </div>
                  <div class="item-detail">
                    <span class="label">الموعد النهائي</span>
                    <span class="value"
                      >{{ project.deadline|date:"Y/m/d" }}</span
                    >
                  </div>
                </div>

                <div class="item-actions">
                  <a
                    href="{% url 'projects:detail' project.pk %}"
                    class="btn btn-primary"
                  >
                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                  </a>
                  <button class="btn btn-outline-primary">
                    <i class="fas fa-edit me-2"></i>تعديل
                  </button>
                </div>
              </div>
              {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
              <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا توجد مشاريع حالياً</h5>
              <p class="text-muted">ابدأ بإنشاء مشروع جديد لتنمية أعمالك</p>
              <button class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إنشاء مشروع جديد
              </button>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
