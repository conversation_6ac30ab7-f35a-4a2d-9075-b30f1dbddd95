{% load table_helpers %}

{% if page_obj.has_other_pages %}
<nav aria-label="Table pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        <!-- Previous page -->
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                    <span class="sr-only">السابق</span>
                </a>
            </li>
        {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                    <span class="sr-only">السابق</span>
                </span>
            </li>
        {% endif %}

        <!-- Page numbers -->
        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">{{ num }}</a>
                </li>
            {% endif %}
        {% endfor %}

        <!-- Next page -->
        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                    <span class="sr-only">التالي</span>
                </a>
            </li>
        {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                    <span class="sr-only">التالي</span>
                </span>
            </li>
        {% endif %}
    </ul>
    
    <!-- Page info -->
    <div class="text-center mt-2">
        <small class="text-muted">
            عرض {{ page_obj.start_index }} إلى {{ page_obj.end_index }} من {{ page_obj.paginator.count }} نتيجة
        </small>
    </div>
</nav>
{% endif %}
