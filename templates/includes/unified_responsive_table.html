{% comment %} Unified Responsive Table Component Usage: {% include
'includes/unified_responsive_table.html' with table_config=table_config
items=items %} Required context variables: - table_config: Dictionary with table
configuration - items: QuerySet or list of items to display table_config
structure: { 'title': 'Table Title', 'icon': 'fas fa-list', 'columns': [ {
'key': 'field_name', 'label': 'Column Label', 'type':
'text|avatar|status|progress|actions|date', 'avatar_field': 'name', # for avatar
type 'status_map': {...}, # for status type 'date_format': 'Y/m/d', # for date
type } ], 'actions': [ { 'label': 'View', 'icon': 'fas fa-eye', 'url_name':
'app:detail', 'class': 'unified-btn-primary', } ] } {% endcomment %}

<div class="unified-responsive-table">
  <!-- Desktop Table Layout (≥992px) -->
  <div class="unified-table-desktop">
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            {% for column in table_config.columns %}
            <th>{{ column.label }}</th>
            {% endfor %} {% if table_config.actions %}
            <th>الإجراءات</th>
            {% endif %}
          </tr>
        </thead>
        <tbody>
          {% for item in items %}
          <tr>
            {% for column in table_config.columns %}
            <td>
              {% if column.type == 'avatar' %}
              <div class="d-flex align-items-center">
                <div class="user-avatar me-3">
                  {% with avatar_text=item|getattr:column.avatar_field %} {{
                  avatar_text.0|default:"?" }} {% endwith %}
                </div>
                <div>
                  <strong>{{ item|getattr:column.key }}</strong>
                  {% if column.subtitle_field %}
                  <br /><small class="text-muted"
                    >{{ item|getattr:column.subtitle_field }}</small
                  >
                  {% endif %}
                </div>
              </div>
              {% elif column.type == 'status' %} {% with
              status_value=item|getattr:column.key %}
              <span
                class="unified-status-badge unified-status-{{ column.status_map|get_item:status_value|default:'info' }}"
              >
                {% if column.display_method %} {{
                item|getattr:column.display_method }} {% else %} {{ status_value
                }} {% endif %}
              </span>
              {% endwith %} {% elif column.type == 'progress' %} {% with
              progress_value=item|getattr:column.key %}
              <div class="unified-progress">
                <div
                  class="unified-progress-bar"
                  style="width: {{ progress_value }}%;"
                ></div>
              </div>
              <div class="unified-progress-text">{{ progress_value }}%</div>
              {% endwith %} {% elif column.type == 'date' %} {{
              item|getattr:column.key|date:column.date_format|default:"Y/m/d" }}
              {% elif column.type == 'currency' %} {{
              item|getattr:column.key|floatformat:2 }} {{
              column.currency|default:"ر.س" }} {% else %} {{
              item|getattr:column.key }} {% endif %}
            </td>
            {% endfor %} {% if table_config.actions %}
            <td>
              <div class="btn-group" role="group">
                {% for action in table_config.actions %}
                <a
                  href="{% url action.url_name item.pk %}"
                  class="btn btn-sm {{ action.class|default:'unified-btn-secondary' }}"
                >
                  <i class="{{ action.icon }}"></i>
                  {% if action.show_label %} {{ action.label }}{% endif %}
                </a>
                {% endfor %}
              </div>
            </td>
            {% endif %}
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>

  <!-- Tablet Card Layout (768px-991px) -->
  <div class="unified-cards-tablet">
    {% for item in items %}
    <div class="unified-card-item">
      <div class="unified-card-header">
        {% with first_column=table_config.columns.0 %} {% if first_column.type
        == 'avatar' %}
        <div class="user-avatar me-3">
          {% with avatar_text=item|getattr:first_column.avatar_field %} {{
          avatar_text.0|default:"?" }} {% endwith %}
        </div>
        {% endif %}
        <div class="flex-grow-1">
          <h6 class="unified-card-title">
            {{ item|getattr:first_column.key }}
          </h6>
          {% if first_column.subtitle_field %}
          <div class="unified-card-subtitle">
            {{ item|getattr:first_column.subtitle_field }}
          </div>
          {% endif %}
        </div>
        {% endwith %} {% for column in table_config.columns %} {% if column.type
        == 'status' %} {% with status_value=item|getattr:column.key %}
        <span
          class="unified-status-badge unified-status-{{ column.status_map|get_item:status_value|default:'info' }}"
        >
          {% if column.display_method %} {{ item|getattr:column.display_method
          }} {% else %} {{ status_value }} {% endif %}
        </span>
        {% endwith %} {% endif %} {% endfor %}
      </div>

      <div class="unified-card-body">
        {% for column in table_config.columns %} {% if column.type != 'avatar'
        and column.type != 'status' %}
        <div class="unified-card-field">
          <div class="unified-card-field-label">{{ column.label }}</div>
          <div class="unified-card-field-value">
            {% if column.type == 'progress' %} {% with
            progress_value=item|getattr:column.key %}
            <div class="unified-progress">
              <div
                class="unified-progress-bar"
                style="width: {{ progress_value }}%;"
              ></div>
            </div>
            <div class="unified-progress-text">{{ progress_value }}%</div>
            {% endwith %} {% elif column.type == 'date' %} {{
            item|getattr:column.key|date:column.date_format|default:"Y/m/d" }}
            {% elif column.type == 'currency' %} {{
            item|getattr:column.key|floatformat:2 }} {{
            column.currency|default:"ر.س" }} {% else %} {{
            item|getattr:column.key }} {% endif %}
          </div>
        </div>
        {% endif %} {% endfor %}
      </div>

      {% if table_config.actions %}
      <div class="unified-card-actions">
        {% for action in table_config.actions %}
        <a
          href="{% url action.url_name item.pk %}"
          class="unified-btn-mobile {{ action.class|default:'unified-btn-secondary' }}"
        >
          <i class="{{ action.icon }}"></i>
          {% if action.show_label %} {{ action.label }}{% endif %}
        </a>
        {% endfor %}
      </div>
      {% endif %}
    </div>
    {% endfor %}
  </div>

  <!-- Mobile Card Layout (<768px) -->
  <div class="unified-cards-mobile">
    {% for item in items %}
    <div class="unified-mobile-card">
      <div class="unified-mobile-header">
        {% with first_column=table_config.columns.0 %} {% if first_column.type
        == 'avatar' %}
        <div class="user-avatar me-3">
          {% with avatar_text=item|getattr:first_column.avatar_field %} {{
          avatar_text.0|default:"?" }} {% endwith %}
        </div>
        {% endif %}
        <div class="flex-grow-1">
          <h6 class="unified-mobile-title">
            {{ item|getattr:first_column.key }}
          </h6>
          {% if first_column.subtitle_field %}
          <div class="unified-mobile-subtitle">
            {{ item|getattr:first_column.subtitle_field }}
          </div>
          {% endif %}
        </div>
        {% endwith %} {% for column in table_config.columns %} {% if column.type
        == 'status' %} {% with status_value=item|getattr:column.key %}
        <span
          class="unified-status-badge unified-status-{{ column.status_map|get_item:status_value|default:'info' }}"
        >
          {% if column.display_method %} {{ item|getattr:column.display_method
          }} {% else %} {{ status_value }} {% endif %}
        </span>
        {% endwith %} {% endif %} {% endfor %}
      </div>

      <div class="unified-mobile-body">
        {% for column in table_config.columns %} {% if column.type != 'avatar'
        and column.type != 'status' %}
        <div class="unified-mobile-field">
          <div class="unified-mobile-field-label">{{ column.label }}</div>
          <div class="unified-mobile-field-value">
            {% if column.type == 'progress' %} {% with
            progress_value=item|getattr:column.key %}
            <div class="unified-progress">
              <div
                class="unified-progress-bar"
                style="width: {{ progress_value }}%;"
              ></div>
            </div>
            <div class="unified-progress-text">{{ progress_value }}%</div>
            {% endwith %} {% elif column.type == 'date' %} {{
            item|getattr:column.key|date:column.date_format|default:"Y/m/d" }}
            {% elif column.type == 'currency' %} {{
            item|getattr:column.key|floatformat:2 }} {{
            column.currency|default:"ر.س" }} {% else %} {{
            item|getattr:column.key }} {% endif %}
          </div>
        </div>
        {% endif %} {% endfor %}
      </div>

      {% if table_config.actions %}
      <div class="unified-mobile-actions">
        {% for action in table_config.actions %}
        <a
          href="{% url action.url_name item.pk %}"
          class="unified-btn-mobile {{ action.class|default:'unified-btn-secondary' }}"
        >
          <i class="{{ action.icon }}"></i>
          {% if action.show_label %} {{ action.label }}{% endif %}
        </a>
        {% endfor %}
      </div>
      {% endif %}
    </div>
    {% endfor %}
  </div>
</div>
