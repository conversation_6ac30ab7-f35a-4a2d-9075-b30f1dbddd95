# Unified Responsive Table Infrastructure

This document explains how to use the unified responsive table system implemented for the Django ERP project.

## Overview

The unified responsive table infrastructure provides a consistent, mobile-friendly way to display data across all screen sizes:

- **Desktop (≥992px):** Traditional HTML tables with full columns
- **Tablet (768px-991px):** Card layout showing 2-3 cards per row
- **Mobile (<768px):** Single-column card layout with stacked information

## Features

- ✅ Automatic responsive behavior based on screen size
- ✅ Arabic RTL support with IBM Plex Sans Arabic font
- ✅ 44px minimum touch targets for mobile interactions
- ✅ Consistent visual styling across all breakpoints
- ✅ Collapsible sidebar with hamburger menu
- ✅ Accessibility features (focus states, keyboard navigation)
- ✅ Reusable components for any data table
- ✅ Support for various data types (text, avatar, status, progress, date, currency)

## File Structure

```
static/
├── css/
│   └── components.css          # Enhanced with unified table styles
└── js/
    └── unified-responsive-tables.js  # JavaScript for responsive behavior

templates/
├── includes/
│   ├── unified_responsive_table.html  # Main table component
│   └── pagination.html               # Pagination component
└── base_optimized.html              # Updated with new CSS/JS includes

core/
└── templatetags/
    └── table_helpers.py             # Template filters and tags
```

## Usage

### 1. Basic Implementation

In your Django view:

```python
def your_list_view(request):
    items = YourModel.objects.all()
    
    table_config = {
        'title': 'Your Table Title',
        'icon': 'fas fa-list',
        'columns': [
            {
                'key': 'name',
                'label': 'Name',
                'type': 'text',
            },
            # ... more columns
        ],
        'actions': [
            {
                'label': 'View',
                'icon': 'fas fa-eye',
                'url_name': 'your_app:detail',
                'class': 'unified-btn-primary',
            },
        ]
    }
    
    return render(request, 'your_template.html', {
        'items': items,
        'table_config': table_config,
    })
```

In your template:

```html
{% extends 'base_optimized.html' %}
{% load table_helpers %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5>{{ table_config.title }}</h5>
    </div>
    <div class="card-body">
        {% if items %}
            {% with table_config=table_config items=items %}
                {% include 'includes/unified_responsive_table.html' %}
            {% endwith %}
        {% else %}
            <!-- Empty state -->
        {% endif %}
    </div>
</div>
{% endblock %}
```

### 2. Column Types

#### Text Column
```python
{
    'key': 'field_name',
    'label': 'Column Label',
    'type': 'text',
}
```

#### Avatar Column
```python
{
    'key': 'name',
    'label': 'User',
    'type': 'avatar',
    'avatar_field': 'name',           # Field to get initial from
    'subtitle_field': 'email',       # Optional subtitle
}
```

#### Status Column
```python
{
    'key': 'status',
    'label': 'Status',
    'type': 'status',
    'status_map': {
        'active': 'success',
        'inactive': 'danger',
        'pending': 'warning',
    },
    'display_method': 'get_status_display',  # Optional method for display text
}
```

#### Progress Column
```python
{
    'key': 'progress',
    'label': 'Progress',
    'type': 'progress',
}
```

#### Date Column
```python
{
    'key': 'created_at',
    'label': 'Created',
    'type': 'date',
    'date_format': 'Y/m/d',         # Optional format
}
```

#### Currency Column
```python
{
    'key': 'amount',
    'label': 'Amount',
    'type': 'currency',
    'currency': 'ر.س',             # Optional currency symbol
}
```

### 3. Actions Configuration

```python
'actions': [
    {
        'label': 'View',
        'icon': 'fas fa-eye',
        'url_name': 'app:detail',
        'class': 'unified-btn-primary',
        'show_label': True,          # Show label on larger screens
    },
    {
        'label': 'Edit',
        'icon': 'fas fa-edit',
        'url_name': 'app:edit',
        'class': 'unified-btn-secondary',
        'show_label': False,         # Icon only
    },
]
```

### 4. Available CSS Classes

#### Button Classes
- `unified-btn-primary` - Primary action button
- `unified-btn-secondary` - Secondary action button
- `unified-btn-mobile` - Base mobile-friendly button

#### Status Badge Classes
- `unified-status-success` - Green success badge
- `unified-status-warning` - Yellow warning badge
- `unified-status-danger` - Red danger badge
- `unified-status-info` - Blue info badge

## Template Filters

The system includes several helpful template filters:

```python
{{ object|getattr:"field_name" }}        # Get attribute value
{{ status|status_class:status_map }}     # Get status CSS class
{{ name|avatar_initial }}               # Get avatar initial
{{ text|truncate_smart:30 }}            # Smart text truncation
{{ date|format_date_arabic:"Y/m/d" }}   # Arabic-friendly date format
{{ phone|phone_format }}                # Format phone numbers
{{ amount|format_currency:"USD" }}      # Format currency
```

## JavaScript API

The JavaScript class `UnifiedResponsiveTables` provides:

- Automatic responsive behavior
- Sidebar toggle functionality
- Touch-friendly interactions
- Keyboard navigation support

## Customization

### Custom Styling

Add custom styles in your template:

```html
{% block extra_css %}
<style>
.your-custom-table .unified-card-item {
    border-left-color: #your-color;
}
</style>
{% endblock %}
```

### Custom JavaScript

Extend functionality:

```html
{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Your custom table interactions
});
</script>
{% endblock %}
```

## Best Practices

1. **Keep column count reasonable** - Max 6-7 columns for desktop
2. **Use appropriate data types** - Choose the right column type for your data
3. **Provide meaningful labels** - Use clear, descriptive Arabic labels
4. **Test on all devices** - Verify responsive behavior works correctly
5. **Use consistent actions** - Stick to standard action patterns across tables

## Examples

See the implemented examples in:
- `clients/views.py` and `templates/clients/list.html`
- `projects/views.py` and `templates/projects/list.html`
- `employees/views.py` and `templates/employees/list.html`

## Troubleshooting

### Table not responsive
- Ensure `{% load table_helpers %}` is at the top of your template
- Check that CSS and JS files are properly loaded
- Verify table_config structure is correct

### Arabic text not displaying correctly
- Confirm IBM Plex Sans Arabic font is loaded
- Check RTL direction is set on container elements
- Verify Arabic text is properly encoded

### Touch targets too small on mobile
- Use `unified-btn-mobile` class for buttons
- Ensure minimum 44px height/width for interactive elements
- Test on actual mobile devices
